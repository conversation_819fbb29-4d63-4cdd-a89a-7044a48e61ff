version: '3.8'

services:
  hrm-hr-service:
    build:
      context: ..
      dockerfile: hrm-hr-service/Dockerfile
    ports:
      - "6020:6020"
    environment:
      # Database Configuration
      - SPRING_DATASOURCE_URL=**********************************
      - SPRING_DATASOURCE_USERNAME=postgres
      - SPRING_DATASOURCE_PASSWORD=ggmacket123
      
      # Kafka Configuration
      - SPRING_KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      
      # JWT Configuration (should match hrm-common settings)
      - JWT_SECRET=mySecretKey123456789012345678901234567890
      - JWT_EXPIRATION=86400000
      
      # Profile
      - SPRING_PROFILES_ACTIVE=docker
    depends_on:
      - postgres
      - kafka
    networks:
      - hrm-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6020/test"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=hr
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=ggmacket123
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - hrm-network
    restart: unless-stopped

  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - hrm-network
    restart: unless-stopped

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: true
    networks:
      - hrm-network
    restart: unless-stopped

volumes:
  postgres_data:

networks:
  hrm-network:
    driver: bridge
